const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Browser<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u, ipc<PERSON>ain, screen, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');
const PlaylistManager = require('./playlist-manager');
const AudioController = require('./audio-controller');
const YouTubeAdSkipper = require('./youtube-ad-skipper');
const VideoProgressMonitor = require('./video-progress-monitor');


// Initialize store for persistent data
const store = new Store();

// Global references
let mainWindow = null;
let tray = null;
let youtubeView = null;
let spotifyView = null;
let isMinimized = false;
let isMuted = false;
let playlistManager = null;
let audioController = null;
let youtubeAdSkipper = null;
let videoProgressMonitor = null;
// Global error tracking structure
const activeErrors = {
  spotifyNotLoggedIn: false,
  // Add more error types here in the future
  // youtubeConnectionError: false,
  // audioSystemError: false,
};

let hasErrors = false; // Track if there are any errors (like Spotify not logged in)
let appInstance = null; // Singleton instance

// Auto-launch setup
const autoLauncher = new AutoLaunch({
  name: 'YSViewer',
  path: app.getPath('exe')
});

class YSViewerApp {
  constructor() {
    if (appInstance) {
      return appInstance;
    }
    appInstance = this;
    this.initializeApp();
  }

  static getInstance() {
    if (!appInstance) {
      appInstance = new YSViewerApp();
    }
    return appInstance;
  }

  async initializeApp() {
    // Initialize managers
    playlistManager = new PlaylistManager();
    audioController = new AudioController();
    youtubeAdSkipper = new YouTubeAdSkipper();
    videoProgressMonitor = new VideoProgressMonitor();

    // Set mute state but don't apply yet (no web contents registered)
    isMuted = true;
    console.log('Application will start muted by default');

    // Setup auto-launch
    await this.setupAutoLaunch();

    // Create main window
    this.createMainWindow();

    // Create system tray
    this.createSystemTray();

    // Setup browser views
    this.setupBrowserViews();

    // Now apply mute state after web contents are registered
    await audioController.setMute(true);
    console.log('Application started muted by default - mute applied to web contents');

    // Start playlist management
    await this.startPlaylistManagement();

    // Start error checking
    this.startErrorChecking();
  }

  async setupAutoLaunch() {
    try {
      const isEnabled = await autoLauncher.isEnabled();
      if (!isEnabled) {
        await autoLauncher.enable();
        console.log('Auto-launch enabled');
      }
    } catch (error) {
      console.error('Failed to setup auto-launch:', error);
    }
  }

  createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;

    mainWindow = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(800, height - 100),
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile(path.join(__dirname, 'electron-index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window minimize - system minimize button should minimize to tray
    mainWindow.on('minimize', (event) => {
      event.preventDefault();
      this.minimizeToTray();
    });
  }

  createSystemTray() {
    try {
      const trayIcon = this.createTrayIcon(hasErrors);
      tray = new Tray(trayIcon);
      console.log('System tray created successfully');

      // Set up tray menu
      this.updateTrayMenu();
    } catch (error) {
      console.log('Tray creation failed, continuing without tray:', error.message);
      tray = null;
    }
  }

  /**
   * Create tray icon with appropriate color based on error state
   * @param {boolean} isError - Whether to create a red (error) icon or normal icon
   * @returns {Electron.NativeImage} - The tray icon
   */
  createTrayIcon(isError = false) {
    const { nativeImage } = require('electron');

    try {
      // Use static icon assets for reliable color display
      const iconFileName = isError ? 'icon-red.png' : 'icon-green.png';
      const iconPath = path.join(__dirname, '../assets', iconFileName);

      console.log(`Loading ${isError ? 'RED (#EE4B2B)' : 'GREEN'} circular tray icon from: ${iconPath}`);

      // Load the icon from file
      let trayIcon = nativeImage.createFromPath(iconPath);

      if (trayIcon.isEmpty()) {
        throw new Error(`Icon file not found or invalid: ${iconPath}`);
      }

      // Resize to 16x16 for tray (Windows standard)
      const resizedIcon = trayIcon.resize({ width: 16, height: 16 });

      console.log(`[Icon Debug] Icon loaded successfully - isEmpty: ${resizedIcon.isEmpty()}, final size: ${resizedIcon.getSize().width}x${resizedIcon.getSize().height}`);
      console.log(`[Icon Debug] Using ${isError ? 'RED error' : 'GREEN normal'} state icon asset`);

      return resizedIcon;

    } catch (error) {
      console.error('Error loading tray icon asset:', error);
      console.log('Falling back to dynamically generated icon');

      // Fallback: Create a simple colored square dynamically
      const size = 16;
      const buffer = Buffer.alloc(size * size * 4);

      // Use exact colors
      const r = isError ? 238 : 0;   // #EE4B2B red or green
      const g = isError ? 75 : 200;
      const b = isError ? 43 : 0;

      for (let i = 0; i < buffer.length; i += 4) {
        buffer[i] = r;       // R
        buffer[i + 1] = g;   // G
        buffer[i + 2] = b;   // B
        buffer[i + 3] = 255; // A - opaque
      }

      console.log(`[Icon Debug] Using fallback square icon - RGB: ${r}, ${g}, ${b}`);
      return nativeImage.createFromBuffer(buffer, { width: size, height: size });
    }
  }

  /**
   * Update tray icon color based on error state
   * @param {boolean} hasErrors - Whether there are errors
   */
  updateTrayIconColor(hasErrors) {
    if (!tray || tray.isDestroyed()) {
      return;
    }

    try {
      const newIcon = this.createTrayIcon(hasErrors);
      tray.setImage(newIcon);
      console.log(`Tray icon updated: ${hasErrors ? 'red (errors)' : 'normal'}`);
    } catch (error) {
      console.error('Failed to update tray icon color:', error);
    }
  }

  /**
   * Update main window icon color based on error state
   * @param {boolean} hasErrors - Whether there are errors
   */
  updateMainWindowIcon(hasErrors) {
    if (!mainWindow || mainWindow.isDestroyed()) {
      return;
    }

    try {
      const newIcon = this.createTrayIcon(hasErrors);
      mainWindow.setIcon(newIcon);
      console.log(`Main window icon updated: ${hasErrors ? 'red (errors)' : 'normal'}`);
    } catch (error) {
      console.error('Failed to update main window icon:', error);
    }
  }

  /**
   * Update all application icons based on error state
   * @param {boolean} hasErrors - Whether there are errors
   */
  updateAllIcons(hasErrors) {
    this.updateTrayIconColor(hasErrors);
    this.updateMainWindowIcon(hasErrors);
  }

  /**
   * Start periodic error checking
   */
  startErrorChecking() {
    // Check immediately
    this.checkForErrors();

    // Check every 10 seconds for faster detection of login status changes
    setInterval(() => {
      this.checkForErrors();
    }, 10000);

    console.log('Error checking started - will check every 10 seconds');
  }

  /**
   * Check for errors and update tray icon accordingly
   */
  async checkForErrors() {
    try {
      let currentHasErrors = false;

      // Check if Spotify is logged in
      const isSpotifyLoggedIn = await this.isSpotifyLoggedIn();
      const previousSpotifyError = activeErrors.spotifyNotLoggedIn;
      activeErrors.spotifyNotLoggedIn = !isSpotifyLoggedIn;

      console.log(`[Error Check] Spotify login status: ${isSpotifyLoggedIn ? 'LOGGED IN' : 'NOT LOGGED IN'}`);

      if (!isSpotifyLoggedIn) {
        currentHasErrors = true;
        if (!previousSpotifyError) {
          console.log('[Error Check] Spotify user not logged in - error added');
        }
      } else if (previousSpotifyError) {
        console.log('[Error Check] Spotify user logged in - error cleared');
      }

      // Check for any active errors
      currentHasErrors = Object.values(activeErrors).some(error => error);

      // Update global error state and all icons if changed
      if (currentHasErrors !== hasErrors) {
        hasErrors = currentHasErrors;
        this.updateAllIcons(hasErrors);

        if (hasErrors) {
          console.log('[Error Check] Errors detected - all icons set to red');
        } else {
          console.log('[Error Check] No errors - all icons set to normal');
        }
      }

      // Send error status and Spotify login status to renderer
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('error-status-update', {
          hasErrors: currentHasErrors,
          activeErrors: { ...activeErrors },
          spotifyLoggedIn: isSpotifyLoggedIn
        });
      }
    } catch (error) {
      console.error('[Error Check] Failed to check for errors:', error);
    }
  }

  setupBrowserViews() {
    const bounds = mainWindow.getBounds();
    const viewWidth = Math.floor(bounds.width / 2);
    const viewHeight = bounds.height - 170; // Leave space for controls and address bars (100 + 70)

    // YouTube view
    youtubeView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.setBrowserView(youtubeView);
    youtubeView.setBounds({ x: 0, y: 170, width: viewWidth, height: viewHeight }); // Start at y: 170 to account for address bars
    youtubeView.webContents.loadURL('https://www.youtube.com');

    // Register YouTube view for audio control, ad skipping, and progress monitoring
    audioController.registerWebContents(youtubeView.webContents);
    youtubeAdSkipper.registerWebContents(youtubeView.webContents);
    videoProgressMonitor.registerWebContents(youtubeView.webContents);

    // Set up video switch callback
    videoProgressMonitor.setVideoSwitchCallback((progressInfo) => {
      console.log(`Video switch triggered at ${progressInfo.progress.toFixed(2)}%`);
      this.playNextVideo();
    });

    console.log('YouTube web contents registered for audio control, ad skipping, and progress monitoring');

    // Spotify view
    spotifyView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.addBrowserView(spotifyView);
    spotifyView.setBounds({ x: viewWidth, y: 170, width: viewWidth, height: viewHeight }); // Start at y: 170 to account for address bars

    // Register Spotify view for audio control
    audioController.registerWebContents(spotifyView.webContents);
    console.log('Spotify web contents registered for audio control');

    // Load Spotify web player
    spotifyView.webContents.loadURL('https://open.spotify.com/');

    // Open developer tools for Spotify tab to help with debugging login detection
    spotifyView.webContents.once('dom-ready', () => {
      spotifyView.webContents.openDevTools({ mode: 'detach' });
      console.log('Spotify DevTools opened for login detection debugging');
    });

    // Handle window resize
    mainWindow.on('resize', () => {
      const newBounds = mainWindow.getBounds();
      const newViewWidth = Math.floor(newBounds.width / 2);
      const newViewHeight = newBounds.height - 170; // Account for controls and address bars

      youtubeView.setBounds({ x: 0, y: 170, width: newViewWidth, height: newViewHeight });
      spotifyView.setBounds({ x: newViewWidth, y: 170, width: newViewWidth, height: newViewHeight });
    });

    // Setup URL tracking for address bars
    this.setupURLTracking();

    // Handle external links and auto-accept cookies
    [youtubeView, spotifyView].forEach((view, index) => {
      const platform = index === 0 ? 'YouTube' : 'Spotify';

      view.webContents.setWindowOpenHandler(({ url }) => {
        // Allow Spotify internal navigation to stay within the app
        if (platform === 'Spotify' && (
          url.includes('open.spotify.com') ||
          url.includes('accounts.spotify.com') ||
          url.includes('spotify.com')
        )) {
          console.log(`[${platform}] Allowing internal navigation to:`, url);
          return { action: 'allow' };
        }

        // For other external links, open in external browser
        console.log(`[${platform}] Opening external link:`, url);
        shell.openExternal(url);
        return { action: 'deny' };
      });

      // Auto-accept cookies when page loads
      view.webContents.on('dom-ready', () => {
        // Re-apply mute state when DOM is ready
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} on DOM ready`);
        }

        // Restart ad detection for YouTube
        if (platform === 'YouTube' && youtubeAdSkipper) {
          youtubeAdSkipper.startAdDetection();
        }

        const appInstance = YSViewerApp.getInstance();
        appInstance.autoAcceptCookies(view.webContents, platform);
      });

      // Also try after navigation
      view.webContents.on('did-finish-load', () => {
        // Re-apply mute state after page loads
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} after page load`);
        }

        // Restart ad detection for YouTube after page loads
        if (platform === 'YouTube' && youtubeAdSkipper) {
          setTimeout(() => {
            youtubeAdSkipper.startAdDetection();
          }, 1000); // Wait 1 second for page to stabilize
        }

        setTimeout(() => {
          const appInstance = YSViewerApp.getInstance();
          appInstance.autoAcceptCookies(view.webContents, platform);
        }, 2000); // Wait 2 seconds for cookie banners to appear
      });
    });
  }

  setupURLTracking() {
    // Track YouTube URL changes
    if (youtubeView && youtubeView.webContents) {
      youtubeView.webContents.on('did-navigate', (event, url) => {
        this.updateAddressBar('youtube', url);
      });

      youtubeView.webContents.on('did-navigate-in-page', (event, url) => {
        this.updateAddressBar('youtube', url);
      });

      youtubeView.webContents.on('did-finish-load', () => {
        const url = youtubeView.webContents.getURL();
        this.updateAddressBar('youtube', url);
      });
    }

    // Track Spotify URL changes
    if (spotifyView && spotifyView.webContents) {
      spotifyView.webContents.on('did-navigate', (event, url) => {
        this.updateAddressBar('spotify', url);
      });

      spotifyView.webContents.on('did-navigate-in-page', (event, url) => {
        this.updateAddressBar('spotify', url);
      });

      spotifyView.webContents.on('did-finish-load', () => {
        const url = spotifyView.webContents.getURL();
        this.updateAddressBar('spotify', url);
      });
    }
  }

  updateAddressBar(platform, url) {
    try {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('update-address-bar', { platform, url });
      }
    } catch (error) {
      console.error(`Failed to update ${platform} address bar:`, error);
    }
  }

  async startPlaylistManagement() {
    try {
      await playlistManager.initialize();

      // Start playing immediately
      setTimeout(() => {
        this.playNextVideo();
      }, 3000); // Wait 3 seconds for views to load

      // Set up periodic playlist checking
      setInterval(() => {
        this.checkAndPlayNext();
      }, 3600000); // Check every hour
    } catch (error) {
      console.error('Failed to start playlist management:', error);
    }
  }

  async playNextVideo() {
    try {
      // Use improved random selection - try mandatory first, then random pool
      let nextYouTubeVideo = await playlistManager.getNextMandatoryVideo('YouTube');

      // If no mandatory videos available, try truly random selection
      if (!nextYouTubeVideo) {
        nextYouTubeVideo = await playlistManager.getAnyRandomVideo('YouTube');
      }

      // Play YouTube video if available
      if (nextYouTubeVideo && (nextYouTubeVideo.url.includes('youtube.com') || nextYouTubeVideo.url.includes('youtu.be'))) {
        console.log('Playing YouTube video:', nextYouTubeVideo.url);

        // Try to find and click recommended video link first
        const clickedRecommended = await this.tryClickRecommendedVideo(nextYouTubeVideo.url);

        if (!clickedRecommended) {
          // Fall back to direct URL loading if no recommended video found
          youtubeView.webContents.loadURL(nextYouTubeVideo.url);
        }

        // Notify progress monitor of new video
        if (videoProgressMonitor) {
          videoProgressMonitor.setCurrentVideo(nextYouTubeVideo.url, nextYouTubeVideo);
        }
      }

      // If still no videos, try any available video from any pool
      if (!nextYouTubeVideo) {
        const anyVideo = await playlistManager.getAnyRandomVideo();
        if (anyVideo) {
          console.log('Playing any available video:', anyVideo.url);

          if (anyVideo.url.includes('youtube.com') || anyVideo.url.includes('youtu.be')) {
            // Try to find and click recommended video link first
            const clickedRecommended = await this.tryClickRecommendedVideo(anyVideo.url);

            if (!clickedRecommended) {
              // Fall back to direct URL loading if no recommended video found
              youtubeView.webContents.loadURL(anyVideo.url);
            }

            // Notify progress monitor of new video
            if (videoProgressMonitor) {
              videoProgressMonitor.setCurrentVideo(anyVideo.url, anyVideo);
            }
          }
        } else {
          console.log('No videos available to play');
        }
      }
    } catch (error) {
      console.error('Failed to play next video:', error);
    }
  }

  async checkAndPlayNext() {
    try {
      const shouldPlay = await playlistManager.shouldPlayNext();
      if (shouldPlay) {
        this.playNextVideo();
      }
    } catch (error) {
      console.error('Failed to check playlist:', error);
    }
  }

  /**
   * Try to find and click a recommended video link on the current YouTube page
   * @param {string} targetUrl - The URL we want to navigate to
   * @returns {boolean} - True if a recommended video was clicked, false otherwise
   */
  async tryClickRecommendedVideo(targetUrl) {
    try {
      if (!youtubeView || !youtubeView.webContents || youtubeView.webContents.isDestroyed()) {
        return false;
      }

      console.log('Searching for recommended video:', targetUrl);

      // Extract video ID from target URL
      const targetVideoId = this.extractVideoId(targetUrl);
      if (!targetVideoId) {
        console.log('Could not extract video ID from URL:', targetUrl);
        return false;
      }

      // Execute JavaScript to find and click recommended video
      const clickResult = await youtubeView.webContents.executeJavaScript(`
        (function() {
          const targetVideoId = '${targetVideoId}';
          console.log('Looking for video ID:', targetVideoId);

          // Find all video links on the page
          const videoLinks = document.querySelectorAll('a[href*="/watch?v="], a[href*="youtu.be/"]');
          console.log('Found', videoLinks.length, 'video links on page');

          for (let link of videoLinks) {
            const href = link.href;
            let videoId = '';

            // Extract video ID from different URL formats
            if (href.includes('/watch?v=')) {
              const match = href.match(/[?&]v=([^&]+)/);
              videoId = match ? match[1] : '';
            } else if (href.includes('youtu.be/')) {
              const match = href.match(/youtu\\.be\\/([^?&]+)/);
              videoId = match ? match[1] : '';
            }

            if (videoId === targetVideoId) {
              console.log('Found matching recommended video link:', href);

              // Preserve any tracking parameters from the current page
              const currentUrl = new URL(window.location.href);
              const linkUrl = new URL(href, window.location.origin);

              // Copy tracking parameters if they exist
              const trackingParams = ['list', 'index', 'pp', 'si', 'feature', 'app', 'itct'];
              trackingParams.forEach(param => {
                if (currentUrl.searchParams.has(param)) {
                  linkUrl.searchParams.set(param, currentUrl.searchParams.get(param));
                }
              });

              // Update the link href with tracking parameters
              link.href = linkUrl.toString();

              // Click the link to navigate naturally
              console.log('Clicking recommended video with tracking:', link.href);
              link.click();
              return true;
            }
          }

          console.log('No matching recommended video found for ID:', targetVideoId);
          return false;
        })();
      `);

      if (clickResult) {
        console.log('Successfully clicked recommended video link');
        return true;
      } else {
        console.log('No matching recommended video found on current page');
        return false;
      }
    } catch (error) {
      console.error('Error trying to click recommended video:', error);
      return false;
    }
  }

  /**
   * Extract video ID from YouTube URL
   * @param {string} url - YouTube URL
   * @returns {string|null} - Video ID or null if not found
   */
  extractVideoId(url) {
    try {
      const urlObj = new URL(url);

      // Handle youtube.com/watch?v= format
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }

      // Handle youtu.be/ format
      if (urlObj.hostname === 'youtu.be') {
        return urlObj.pathname.substring(1); // Remove leading slash
      }

      return null;
    } catch (error) {
      console.error('Error extracting video ID from URL:', url, error);
      return null;
    }
  }



  /**
   * Check if user is logged in to Spotify
   * @returns {Promise<boolean>} - True if user is logged in to Spotify
   */
  async isSpotifyLoggedIn() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        return false;
      }

      const loginStatus = await spotifyView.webContents.executeJavaScript(`
        (function() {
          const currentUrl = window.location.href;
          console.log('[Spotify Login Check] === STARTING DETECTION ===');
          console.log('[Spotify Login Check] Current URL:', currentUrl);

          // Method 1: Check for login/signup buttons and links (indicates NOT logged in)
          const loginSelectors = [
            // Common login button selectors
            'button[data-testid="login-button"]',
            'a[data-testid="login-button"]',
            'button[data-testid="signup-button"]',
            'a[data-testid="signup-button"]',
            // Link-based selectors
            'a[href*="/login"]',
            'a[href*="/signup"]',
            'a[href*="accounts.spotify.com/login"]',
            'a[href*="accounts.spotify.com/register"]',
            // Text-based button detection
            'button:contains("Log in")',
            'button:contains("Sign up")',
            'a:contains("Log in")',
            'a:contains("Sign up")'
          ];

          let foundLoginElement = null;
          let loginSelector = null;

          for (const selector of loginSelectors) {
            try {
              // Skip :contains() selectors as they don't work in standard DOM
              if (selector.includes(':contains(')) continue;

              foundLoginElement = document.querySelector(selector);
              if (foundLoginElement && foundLoginElement.offsetParent !== null) {
                loginSelector = selector;
                console.log('[Spotify Login Check] Found login element:', selector);
                break;
              }
            } catch (e) {
              // Skip invalid selectors
            }
          }

          // Method 2: Check for text-based login/signup buttons
          const allButtons = Array.from(document.querySelectorAll('button, a'));
          const loginTextButton = allButtons.find(btn => {
            if (!btn.offsetParent) return false; // Skip hidden elements
            const text = btn.textContent.toLowerCase().trim();
            return text === 'log in' || text === 'sign up' || text === 'login' || text === 'signup';
          });

          if (loginTextButton) {
            console.log('[Spotify Login Check] Found login text button:', loginTextButton.textContent.trim());
          }

          // Method 3: Check for login forms (indicates NOT logged in)
          const loginForm = document.querySelector('#login-username') ||
                           document.querySelector('input[name="username"]') ||
                           document.querySelector('input[type="email"]') ||
                           document.querySelector('.login-form') ||
                           document.querySelector('[data-testid="login-username"]');

          // Method 4: Check for STRONG logged-in indicators
          const userIndicators = [
            // User profile/avatar selectors
            '[data-testid="user-widget-link"]',
            '[data-testid="user-widget"]',
            'button[data-testid="user-widget-link"]',
            '[data-testid="user-avatar"]',
            'img[alt*="profile"]',
            'img[alt*="avatar"]',
            // Web player selectors (strong indicator of being logged in)
            '[data-testid="now-playing-widget"]',
            '.Root__now-playing-bar',
            '[data-testid="control-button-playpause"]',
            '[data-testid="player-controls"]',
            '.player-controls',
            // Account/premium indicators
            '[data-testid="premium-button"]',
            'a[href*="/account"]',
            '[data-testid="account-menu"]'
          ];

          let strongUserIndicator = null;
          let userSelector = null;

          for (const selector of userIndicators) {
            strongUserIndicator = document.querySelector(selector);
            if (strongUserIndicator && strongUserIndicator.offsetParent !== null) {
              userSelector = selector;
              console.log('[Spotify Login Check] Found user indicator:', selector);
              break;
            }
          }

          // Method 5: URL-based detection
          const isOnLoginPage = currentUrl.includes('/login') || currentUrl.includes('accounts.spotify.com');
          const isOnMainSpotify = currentUrl.includes('open.spotify.com');

          // Debug logging with comprehensive results
          console.log('[Spotify Login Check] === COMPREHENSIVE RESULTS ===');
          console.log('[Spotify Login Check] Found login element:', !!foundLoginElement, loginSelector || 'none');
          console.log('[Spotify Login Check] Found login text button:', !!loginTextButton);
          console.log('[Spotify Login Check] Found login form:', !!loginForm);
          console.log('[Spotify Login Check] Found strong user indicator:', !!strongUserIndicator, userSelector || 'none');
          console.log('[Spotify Login Check] Is on login page:', isOnLoginPage);
          console.log('[Spotify Login Check] Is on main Spotify:', isOnMainSpotify);

          // DECISION LOGIC: Comprehensive and accurate
          const hasLoginIndicators = !!foundLoginElement || !!loginTextButton || !!loginForm || isOnLoginPage;
          const hasUserIndicators = !!strongUserIndicator;

          console.log('[Spotify Login Check] === FINAL DECISION ===');
          console.log('  - Has ANY login indicators:', hasLoginIndicators);
          console.log('  - Has strong user indicators:', hasUserIndicators);
          console.log('  - Is on main Spotify:', isOnMainSpotify);

          // Rule 1: If we find ANY login indicators, user is definitely NOT logged in
          if (hasLoginIndicators) {
            console.log('[Spotify Login Check] RESULT: NOT LOGGED IN (login indicators found)');
            return false;
          }

          // Rule 2: If we have strong user indicators and no login indicators, user is logged in
          if (hasUserIndicators && isOnMainSpotify) {
            console.log('[Spotify Login Check] RESULT: LOGGED IN (strong user indicators found, no login indicators)');
            return true;
          }

          // Rule 3: If on main Spotify with no login indicators and no strong user indicators,
          // wait for page to fully load (might be in transition)
          if (isOnMainSpotify && !hasLoginIndicators) {
            console.log('[Spotify Login Check] RESULT: LOGGED IN (on main Spotify, no login indicators - assuming logged in)');
            return true;
          }

          // Rule 4: Default to NOT logged in for safety
          console.log('[Spotify Login Check] RESULT: NOT LOGGED IN (default case)');
          return false;
        })();
      `);

      return loginStatus;
    } catch (error) {
      console.error('Error checking Spotify login status:', error);
      return false;
    }
  }
  /**
   * Perform automated Spotify login
   * @param {string} username - Spotify username/email
   * @param {string} password - Spotify password
   * @returns {Promise<boolean>} - True if login successful
   */
  async performSpotifyLogin(username, password) {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        console.error('Spotify view not available for login');
        return false;
      }

      // Navigate to login page if not already there
      const currentUrl = spotifyView.webContents.getURL();
      if (!currentUrl.includes('accounts.spotify.com') && !currentUrl.includes('login')) {
        await spotifyView.webContents.loadURL('https://accounts.spotify.com/login');
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for page load
      }

      // Attempt to fill login form and submit
      const loginResult = await spotifyView.webContents.executeJavaScript(`
        (function() {
          try {
            // Find username/email field
            const usernameField = document.querySelector('#login-username') ||
                                 document.querySelector('input[name="username"]') ||
                                 document.querySelector('input[type="email"]') ||
                                 document.querySelector('[data-testid="login-username"]');

            // Find password field
            const passwordField = document.querySelector('#login-password') ||
                                 document.querySelector('input[name="password"]') ||
                                 document.querySelector('input[type="password"]') ||
                                 document.querySelector('[data-testid="login-password"]');

            // Find login button
            const loginButton = document.querySelector('#login-button') ||
                               document.querySelector('button[type="submit"]') ||
                               document.querySelector('[data-testid="login-button"]') ||
                               document.querySelector('button[data-testid="login-button"]');

            if (usernameField && passwordField && loginButton) {
              usernameField.value = '${username}';
              passwordField.value = '${password}';

              // Trigger input events
              usernameField.dispatchEvent(new Event('input', { bubbles: true }));
              passwordField.dispatchEvent(new Event('input', { bubbles: true }));

              // Click login button
              loginButton.click();
              return true;
            }

            return false;
          } catch (error) {
            console.error('Login automation error:', error);
            return false;
          }
        })();
      `);

      if (loginResult) {
        // Wait for login to complete and check if successful
        await new Promise(resolve => setTimeout(resolve, 5000));
        return await this.isSpotifyLoggedIn();
      }

      return false;
    } catch (error) {
      console.error('Error performing Spotify login:', error);
      return false;
    }
  }

  /**
   * Perform automated Spotify logout
   * @returns {Promise<boolean>} - True if logout successful
   */
  async performSpotifyLogout() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        console.error('Spotify view not available for logout');
        return false;
      }

      // Attempt to find and click logout button
      const logoutResult = await spotifyView.webContents.executeJavaScript(`
        (function() {
          try {
            // Look for user menu first
            const userMenu = document.querySelector('[data-testid="user-widget"]') ||
                           document.querySelector('.user-widget') ||
                           document.querySelector('[data-testid="user-menu"]');

            if (userMenu) {
              userMenu.click();

              // Wait a bit for menu to open
              setTimeout(() => {
                const logoutButton = document.querySelector('[data-testid="logout-button"]') ||
                                   document.querySelector('a[href*="logout"]') ||
                                   document.querySelector('button[data-testid="logout"]') ||
                                   document.querySelector('[data-testid="user-menu-logout"]');

                if (logoutButton) {
                  logoutButton.click();
                  return true;
                }
              }, 500);
            }

            // Direct logout attempt
            const directLogout = document.querySelector('[data-testid="logout-button"]') ||
                               document.querySelector('a[href*="logout"]') ||
                               document.querySelector('button[data-testid="logout"]');

            if (directLogout) {
              directLogout.click();
              return true;
            }

            return false;
          } catch (error) {
            console.error('Logout automation error:', error);
            return false;
          }
        })();
      `);

      if (logoutResult) {
        // Wait for logout to complete
        await new Promise(resolve => setTimeout(resolve, 3000));
        return !(await this.isSpotifyLoggedIn());
      }

      return false;
    } catch (error) {
      console.error('Error performing Spotify logout:', error);
      return false;
    }
  }

  /**
   * Start recording Spotify login flow
   * @returns {Promise<boolean>} - True if recording started successfully
   */
  async startSpotifyLoginRecording() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        console.error('Spotify view not available for recording');
        return false;
      }

      // Navigate to login page
      await spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      console.log('Spotify login recording started - user should login manually');
      console.log('Recording functionality would capture user interactions here');

      // In a full implementation, this would set up event listeners to record
      // user interactions (clicks, form fills, etc.) for later playback

      return true;
    } catch (error) {
      console.error('Error starting Spotify login recording:', error);
      return false;
    }
  }





  async toggleSystemAudio() {
    try {
      isMuted = await audioController.toggleMute();
      this.updateTrayMenu();

      // Notify renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        try {
          mainWindow.webContents.send('audio-state-changed', { isMuted });
        } catch (sendError) {
          console.warn('Failed to send audio state to renderer:', sendError.message);
        }
      }

      return isMuted;
    } catch (error) {
      console.error('Failed to toggle audio:', error);
      throw new Error(`Audio toggle failed: ${error.message}`);
    }
  }

  updateTrayMenu() {
    if (tray && !tray.isDestroyed()) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'YSViewer',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: isMuted ? 'Unmute Application' : 'Mute Application',
          type: 'normal',
          click: () => this.toggleSystemAudio()
        },
        { type: 'separator' },
        {
          label: 'Show Window',
          type: 'normal',
          click: () => this.showMainWindow()
        },
        {
          label: 'Minimize to Dot',
          type: 'normal',
          click: () => this.minimizeToDot()
        },
        { type: 'separator' },
        {
          label: 'Quit',
          type: 'normal',
          click: () => {
            app.isQuiting = true;
            app.quit();
          }
        }
      ]);
      tray.setContextMenu(contextMenu);
    }
  }

  showMainWindow() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        if (isMinimized) {
          // Restore from minimized state
          mainWindow.setSize(1400, 800);
          mainWindow.center();
          isMinimized = false;
        }
        mainWindow.show();
        mainWindow.focus();
        result = { success: true, windowVisible: true, wasMinimized: isMinimized };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    }
  }

  minimizeToTray() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        mainWindow.hide();
        result = { success: true, windowHidden: true };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    }
  }

  minimizeToDot() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        const { width, height } = screen.getPrimaryDisplay().workAreaSize;
        mainWindow.setSize(50, 50); // Changed from 5x5 to 50x50
        mainWindow.setPosition(width - 50, height - 50);
        mainWindow.setAlwaysOnTop(true);
        mainWindow.setSkipTaskbar(true);
        isMinimized = true;
        result = {
          success: true,
          windowMinimized: true,
          position: { x: width - 50, y: height - 50 },
          size: { width: 50, height: 50 }
        };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } 
  }











  async autoAcceptCookies(webContents, platform) {
    try {
      // Check if cookies have already been accepted for this platform
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      const alreadyAccepted = store.get(cookieAcceptanceKey, false);

      if (alreadyAccepted) {
        console.log(`Cookies already accepted for ${platform}, skipping auto-accept`);
        return;
      }

      console.log(`Attempting to auto-accept cookies for ${platform}`);

      let cookieAccepted = false;

      if (platform === 'YouTube') {
        // YouTube cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for YouTube cookie consent (updated for 2024)
            const selectors = [
              // Modern YouTube cookie consent selectors
              'button[aria-label*="Accept all"]',
              'button[aria-label*="Accept All"]',
              'button[aria-label*="ACCEPT ALL"]',
              'button[aria-label*="Accept"]',
              'button[aria-label*="accept"]',
              'button[aria-label*="I agree"]',
              'button[aria-label*="Agree"]',
              // YouTube specific selectors
              '.VfPpkd-LgbsSe[jsname="tWT92d"]',
              'button[jsname="tWT92d"]',
              'button[data-testid="accept-button"]',
              'button[data-testid="accept-all-button"]',
              // Generic consent selectors
              'button[id*="accept"]',
              'button[class*="accept"]',
              'button[class*="consent"]',
              // GDPR/Cookie banner frameworks
              '.fc-consent-root button[data-testid="fc-confirm-choices"]',
              '.fc-consent-root button[aria-label*="Consent"]',
              '#consent-bump button',
              '.consent-bump button',
              // OneTrust selectors
              '#onetrust-accept-btn-handler',
              '.onetrust-accept-btn-handler',
              'button[id*="onetrust"][id*="accept"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null && !button.disabled) {
                console.log('Found YouTube cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content with more comprehensive search
            const buttons = document.querySelectorAll('button, [role="button"]');
            for (let button of buttons) {
              if (!button.offsetParent || button.disabled) continue;

              const text = button.textContent.toLowerCase().trim();
              const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();
              const title = (button.getAttribute('title') || '').toLowerCase();

              const acceptTexts = [
                'accept all', 'accept all cookies', 'accept cookies',
                'i agree', 'agree', 'allow all', 'allow cookies',
                'ok', 'got it', 'continue', 'proceed'
              ];

              for (let acceptText of acceptTexts) {
                if (text.includes(acceptText) || ariaLabel.includes(acceptText) || title.includes(acceptText)) {
                  console.log('Found YouTube cookie button by text:', text || ariaLabel || title);
                  button.click();
                  return true;
                }
              }
            }

            return false;
          })();
        `);
      } else if (platform === 'Spotify') {
        // Spotify cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for Spotify cookie consent (updated for 2024)
            const selectors = [
              // Spotify specific selectors
              'button[data-testid="accept-all-cookies"]',
              'button[data-testid="accept-cookies"]',
              'button[data-testid="cookie-accept"]',
              'button[data-cy="accept-all-cookies"]',
              'button[data-cy="accept-cookies"]',
              // OneTrust framework (commonly used by Spotify)
              '#onetrust-accept-btn-handler',
              '.onetrust-accept-btn-handler',
              'button[id*="onetrust"][id*="accept"]',
              'button[class*="onetrust"][class*="accept"]',
              // CookieBot framework
              '#CybotCookiebotDialogBodyLevelButtonLevelOptinAllowAll',
              'button[id*="CybotCookiebot"][id*="Allow"]',
              // Generic consent selectors
              'button[id*="accept"]',
              'button[class*="accept"]',
              'button[class*="consent"]',
              'button[data-qa="accept"]',
              'button[data-qa="accept-all"]',
              // GDPR banner selectors
              '.gdpr-banner button[data-accept]',
              '.cookie-banner button[data-accept]',
              '.consent-banner button',
              // Quantcast Choice framework
              '.qc-cmp2-summary-buttons button[mode="primary"]',
              '.qc-cmp2-summary-buttons button:last-child'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null && !button.disabled) {
                console.log('Found Spotify cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content with more comprehensive search
            const buttons = document.querySelectorAll('button, [role="button"], input[type="button"]');
            for (let button of buttons) {
              if (!button.offsetParent || button.disabled) continue;

              const text = button.textContent.toLowerCase().trim();
              const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();
              const title = (button.getAttribute('title') || '').toLowerCase();
              const value = (button.getAttribute('value') || '').toLowerCase();

              const acceptTexts = [
                'accept all', 'accept all cookies', 'accept cookies',
                'allow all', 'allow cookies', 'allow all cookies',
                'i agree', 'agree', 'agree to all', 'agree & close',
                'ok', 'got it', 'continue', 'proceed',
                'yes, i agree', 'accept and continue',
                'accept & close', 'save & exit'
              ];

              for (let acceptText of acceptTexts) {
                if (text.includes(acceptText) || ariaLabel.includes(acceptText) ||
                    title.includes(acceptText) || value.includes(acceptText)) {
                  console.log('Found Spotify cookie button by text:', text || ariaLabel || title || value);
                  button.click();
                  return true;
                }
              }
            }

            return false;
          })();
        `);
      }

      // If cookies were successfully accepted, mark it as completed
      if (cookieAccepted) {
        store.set(cookieAcceptanceKey, true);
        console.log(`Cookies accepted for ${platform} - marked as completed, will not try again`);
      }
    } catch (error) {
      console.log(`Failed to auto-accept cookies for ${platform}:`, error.message);
    }
  }

  // Utility method to reset cookie acceptance flags (for testing/troubleshooting)
  resetCookieAcceptance(platform = null) {
    if (platform) {
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      store.delete(cookieAcceptanceKey);
      console.log(`Reset cookie acceptance flag for ${platform}`);
    } else {
      // Reset all platforms
      store.delete('cookiesAccepted_YouTube');
      store.delete('cookiesAccepted_Spotify');
      console.log('Reset cookie acceptance flags for all platforms');
    }
  }












}

// App event handlers
app.whenReady().then(() => {
  YSViewerApp.getInstance();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    YSViewerApp.getInstance().showMainWindow();
  }
});

// IPC handlers
ipcMain.handle('get-app-state', () => {
  return {
    isMuted,
    isMinimized
  };
});

ipcMain.handle('toggle-audio', async () => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();
    await app.toggleSystemAudio();
    result = { success: true, isMuted };
    return result;
  } catch (err) {
    error = err;
    console.error('IPC toggle-audio error:', err);
    result = { success: false, error: err.message };
    return result;
  }
});

ipcMain.handle('minimize-to-tray', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToTray();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-tray error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('close-application', () => {
  try {
    app.isQuiting = true;
    app.quit();
    return { success: true };
  } catch (error) {
    console.error('IPC close-application error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('minimize-to-dot', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToDot();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-dot error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-window', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.showMainWindow();
    return { success: true };
  } catch (error) {
    console.error('IPC show-window error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-cookie-acceptance', (_, platform) => {
  try {
    const app = YSViewerApp.getInstance();
    app.resetCookieAcceptance(platform);
    return { success: true };
  } catch (error) {
    console.error('IPC reset-cookie-acceptance error:', error);
    return { success: false, error: error.message };
  }
});



ipcMain.handle('verify-mute-state', () => {
  try {
    audioController.verifyMuteState();
    return { success: true };
  } catch (error) {
    console.error('IPC verify-mute-state error:', error);
    return { success: false, error: error.message };
  }
});



ipcMain.handle('reset-ad-skipper-stats', () => {
  try {
    youtubeAdSkipper.resetStats();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-ad-skipper-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('debug-ad-detection', async () => {
  try {
    await youtubeAdSkipper.debugAdDetection();
    return { success: true };
  } catch (error) {
    console.error('IPC debug-ad-detection error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-stats', () => {
  try {
    const stats = videoProgressMonitor.getProgressStats();
    return { success: true, stats };
  } catch (error) {
    console.error('IPC get-progress-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-history', () => {
  try {
    const history = videoProgressMonitor.getProgressHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-progress-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('toggle-progress-monitor', () => {
  try {
    if (videoProgressMonitor.isEnabled) {
      videoProgressMonitor.disable();
    } else {
      videoProgressMonitor.enable();
    }
    return { success: true, isEnabled: videoProgressMonitor.isEnabled };
  } catch (error) {
    console.error('IPC toggle-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-progress-monitor', () => {
  try {
    videoProgressMonitor.reset();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-detection-history', () => {
  try {
    const history = youtubeAdSkipper.getAdDetectionHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-ad-detection-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-skip-attempt-history', () => {
  try {
    const history = youtubeAdSkipper.getSkipAttemptHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-skip-attempt-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-skipper-mock-data', () => {
  try {
    const mockData = youtubeAdSkipper.getMockData();
    return { success: true, mockData };
  } catch (error) {
    console.error('IPC get-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-ad-skipper-mock-data', () => {
  try {
    youtubeAdSkipper.clearMockData();
    return { success: true };
  } catch (error) {
    console.error('IPC clear-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});

// Spotify IPC handlers
ipcMain.handle('spotify-login', async (_, { username, password }) => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();

    // Save credentials to store
    store.set('spotify.credentials', { username, password });
    console.log('Spotify credentials saved');

    // Attempt automated login
    const loginSuccess = await app.performSpotifyLogin(username, password);

    if (loginSuccess) {
      result = { success: true, message: 'Login successful' };
      // Trigger immediate error check to update tray icon
      setTimeout(() => app.checkForErrors(), 1000);
    } else {
      result = { success: false, error: 'Login failed - please try manual login or record flow' };
    }

    return result;
  } catch (err) {
    error = err;
    console.error('IPC spotify-login error:', err);
    result = { success: false, error: err.message };
    return result;
  }
});

ipcMain.handle('spotify-logout', async () => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();

    // Clear saved credentials
    store.delete('spotify.credentials');
    console.log('Spotify credentials cleared');

    // Attempt automated logout
    const logoutSuccess = await app.performSpotifyLogout();

    if (logoutSuccess) {
      result = { success: true, message: 'Logout successful' };
      // Trigger immediate error check to update tray icon
      setTimeout(() => app.checkForErrors(), 1000);
    } else {
      result = { success: false, error: 'Logout failed - please logout manually' };
    }

    return result;
  } catch (err) {
    error = err;
    console.error('IPC spotify-logout error:', err);
    result = { success: false, error: err.message };
    return result;
  }
});

ipcMain.handle('spotify-record-login', async () => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();

    // Start recording login flow
    const recordingStarted = await app.startSpotifyLoginRecording();

    if (recordingStarted) {
      result = { success: true, message: 'Login recording started - please login manually' };
    } else {
      result = { success: false, error: 'Failed to start login recording' };
    }

    return result;
  } catch (err) {
    error = err;
    console.error('IPC spotify-record-login error:', err);
    result = { success: false, error: err.message };
    return result;
  }
});

// Get current error status
ipcMain.handle('get-error-status', () => {
  try {
    return {
      success: true,
      hasErrors,
      activeErrors: { ...activeErrors }
    };
  } catch (error) {
    console.error('IPC get-error-status error:', error);
    return { success: false, error: error.message };
  }
});
