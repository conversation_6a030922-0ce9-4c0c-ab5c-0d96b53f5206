const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class YSViewerRenderer {
  constructor() {
    this.isAudioMuted = false;
    this.isMinimized = false;
    this.initializeUI();
    this.setupEventListeners();
    this.loadAppState();
  }

  initializeUI() {
    // Get DOM elements
    this.audioBtn = document.getElementById('audio-btn');
    this.audioIcon = document.getElementById('audio-icon');
    this.audioText = document.getElementById('audio-text');
    this.minimizeBtn = document.getElementById('minimize-btn');
    this.closeBtn = document.getElementById('close-btn');
    this.statusText = document.getElementById('status-text');
    this.notification = document.getElementById('notification');
    this.notificationText = document.getElementById('notification-text');

    // Spotify login elements
    this.spotifyUsername = document.getElementById('spotify-username');
    this.spotifyPassword = document.getElementById('spotify-password');
    this.spotifyLoginBtn = document.getElementById('spotify-login-btn');
    this.spotifyRecordBtn = document.getElementById('spotify-record-btn');
    this.spotifyLogoutBtn = document.getElementById('spotify-logout-btn');

    // Address bar elements
    this.youtubeAddress = document.getElementById('youtube-address');
    this.spotifyAddress = document.getElementById('spotify-address');
  }

  setupEventListeners() {
    // Audio control button
    this.audioBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('toggle-audio');
        if (result.success) {
          this.isAudioMuted = result.isMuted;
          this.updateAudioButton();
          this.showNotification(
            this.isAudioMuted ? 'Application audio muted' : 'Application audio unmuted',
            'success'
          );
        } else {
          throw new Error(result.error || 'Unknown error');
        }
      } catch (error) {
        console.error('Failed to toggle audio:', error);
        this.showNotification('Failed to toggle audio: ' + error.message, 'error');
      }
    });



    // Minimize to tray button
    this.minimizeBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('minimize-to-tray');
        if (result.success) {
          this.showNotification('Minimized to tray', 'info');
        } else {
          throw new Error(result.error || 'Unknown error');
        }
      } catch (error) {
        console.error('Failed to minimize to tray:', error);
        this.showNotification('Failed to minimize: ' + error.message, 'error');
      }
    });

    // Close application button
    this.closeBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('close-application');
        if (!result.success) {
          throw new Error(result.error || 'Unknown error');
        }
      } catch (error) {
        console.error('Failed to close application:', error);
        this.showNotification('Failed to close: ' + error.message, 'error');
      }
    });

    // Spotify login button
    this.spotifyLoginBtn.addEventListener('click', async () => {
      const username = this.spotifyUsername.value.trim();
      const password = this.spotifyPassword.value.trim();

      if (!username || !password) {
        this.showNotification('Please enter both username and password', 'error');
        return;
      }

      try {
        this.spotifyLoginBtn.disabled = true;
        this.spotifyLoginBtn.textContent = 'Logging in...';

        const result = await ipcRenderer.invoke('spotify-login', { username, password });
        if (result.success) {
          this.showNotification('Spotify login successful', 'success');
        } else {
          throw new Error(result.error || 'Login failed');
        }
      } catch (error) {
        console.error('Failed to login to Spotify:', error);
        this.showNotification('Spotify login failed: ' + error.message, 'error');
      } finally {
        this.spotifyLoginBtn.disabled = false;
        this.spotifyLoginBtn.textContent = 'Login';
      }
    });

    // Spotify record login button
    this.spotifyRecordBtn.addEventListener('click', async () => {
      try {
        this.spotifyRecordBtn.disabled = true;
        this.spotifyRecordBtn.textContent = 'Recording...';

        const result = await ipcRenderer.invoke('spotify-record-login');
        if (result.success) {
          this.showNotification('Login recording started - please login manually', 'info');
        } else {
          throw new Error(result.error || 'Recording failed');
        }
      } catch (error) {
        console.error('Failed to start recording:', error);
        this.showNotification('Recording failed: ' + error.message, 'error');
      } finally {
        this.spotifyRecordBtn.disabled = false;
        this.spotifyRecordBtn.textContent = 'Record';
      }
    });

    // Spotify logout button
    this.spotifyLogoutBtn.addEventListener('click', async () => {
      try {
        this.spotifyLogoutBtn.disabled = true;
        this.spotifyLogoutBtn.textContent = 'Logging out...';

        const result = await ipcRenderer.invoke('spotify-logout');
        if (result.success) {
          this.showNotification('Spotify logout successful', 'success');
        } else {
          throw new Error(result.error || 'Logout failed');
        }
      } catch (error) {
        console.error('Failed to logout from Spotify:', error);
        this.showNotification('Spotify logout failed: ' + error.message, 'error');
      } finally {
        this.spotifyLogoutBtn.disabled = false;
        this.spotifyLogoutBtn.textContent = 'Logout';
      }
    });

    // Listen for audio state changes from main process
    ipcRenderer.on('audio-state-changed', (_, { isMuted }) => {
      this.isAudioMuted = isMuted;
      this.updateAudioButton();
    });

    // Listen for playlist updates
    ipcRenderer.on('playlist-updated', (_, data) => {
      this.updateStatus(`Playing: ${data.title || 'Unknown'}`);
      this.showNotification(`Now playing: ${data.platform}`, 'info');
    });

    // Listen for app state changes
    ipcRenderer.on('app-state-changed', (_, state) => {
      this.isAudioMuted = state.isMuted;
      this.isMinimized = state.isMinimized;
      this.updateUI();
    });

    // Listen for notifications from main process
    ipcRenderer.on('show-notification', (_, { message, type, duration }) => {
      this.showNotification(message, type, duration);
    });

    // Listen for address bar updates
    ipcRenderer.on('update-address-bar', (_, { platform, url }) => {
      this.updateAddressBar(platform, url);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'm':
            event.preventDefault();
            this.audioBtn.click();
            break;
          case 'h':
            event.preventDefault();
            this.closeBtn.click();
            break;
          case 'd':
            event.preventDefault();
            this.minimizeBtn.click();
            break;
          case 't':
            event.preventDefault();
            this.debugAdDetection();
            break;
        }
      }
    });

    // Window focus events
    window.addEventListener('focus', () => {
      if (this.isMinimized) {
        this.isMinimized = false;
        this.showNotification('Window restored', 'info');
      }
    });

    // Prevent context menu on production
    if (!process.argv.includes('--dev')) {
      document.addEventListener('contextmenu', (event) => {
        event.preventDefault();
      });
    }
  }

  async loadAppState() {
    try {
      const state = await ipcRenderer.invoke('get-app-state');
      this.isAudioMuted = state.isMuted;
      this.isMinimized = state.isMinimized;



      this.updateUI();
      this.updateStatus('Application loaded');
    } catch (error) {
      console.error('Failed to load app state:', error);
      this.updateStatus('Failed to load state');
    }
  }

  updateUI() {
    this.updateAudioButton();
    this.updateMinimizeButton();
  }

  updateAudioButton() {
    if (this.isAudioMuted) {
      this.audioIcon.textContent = '🔇';
      this.audioText.textContent = 'Unmute';
      this.audioBtn.classList.add('active');
      this.audioBtn.title = 'Unmute System Audio';
    } else {
      this.audioIcon.textContent = '🔊';
      this.audioText.textContent = 'Mute';
      this.audioBtn.classList.remove('active');
      this.audioBtn.title = 'Mute System Audio';
    }
  }



  updateMinimizeButton() {
    // The minimize button always minimizes to tray, no state change needed
    this.minimizeBtn.title = 'Minimize to Tray';
  }

  updateStatus(message) {
    this.statusText.textContent = message;
    console.log('Status:', message);
  }

  showNotification(message, type = 'info', duration = 3000) {
    this.notificationText.textContent = message;
    this.notification.className = `notification ${type}`;
    this.notification.classList.add('show');

    // Auto-hide after specified duration
    setTimeout(() => {
      this.notification.classList.remove('show');
    }, duration);
  }

  updateAddressBar(platform, url) {
    try {
      if (platform === 'youtube' && this.youtubeAddress) {
        this.youtubeAddress.value = url;
        this.youtubeAddress.title = url; // Show full URL on hover
      } else if (platform === 'spotify' && this.spotifyAddress) {
        this.spotifyAddress.value = url;
        this.spotifyAddress.title = url; // Show full URL on hover
      }
    } catch (error) {
      console.error(`Failed to update ${platform} address bar:`, error);
    }
  }

  // Utility methods for external access
  async toggleAudio() {
    this.audioBtn.click();
  }

  async verifyMuteState() {
    try {
      const result = await ipcRenderer.invoke('verify-mute-state');
      if (result.success) {
        this.showNotification('Mute state verification logged to console', 'info');
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to verify mute state:', error);
      this.showNotification('Failed to verify mute state: ' + error.message, 'error');
    }
  }



  async minimizeToTray() {
    this.minimizeBtn.click();
  }

  async minimizeToDot() {
    try {
      const result = await ipcRenderer.invoke('minimize-to-dot');
      if (result.success) {
        this.isMinimized = true;
        this.updateMinimizeButton();
        this.showNotification('Minimized to dot (50x50)', 'info');
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to minimize to dot:', error);
      this.showNotification('Failed to minimize: ' + error.message, 'error');
    }
  }

  async closeApplication() {
    this.closeBtn.click();
  }

  async debugAdDetection() {
    try {
      const result = await ipcRenderer.invoke('debug-ad-detection');
      if (result.success) {
        this.showNotification('Ad detection debug triggered - check console', 'info');
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to trigger ad detection debug:', error);
      this.showNotification('Failed to debug ad detection: ' + error.message, 'error');
    }
  }
}

// Initialize the renderer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.ysviewer = new YSViewerRenderer();
  
  // Show welcome message
  setTimeout(() => {
    window.ysviewer.showNotification('YSViewer loaded successfully!', 'success');
  }, 1000);
});

// Handle unhandled errors
window.addEventListener('error', (event) => {
  console.error('Renderer error:', event.error);
  if (window.ysviewer) {
    window.ysviewer.showNotification('An error occurred', 'error');
  }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  if (window.ysviewer) {
    window.ysviewer.showNotification('An error occurred', 'error');
  }
});
