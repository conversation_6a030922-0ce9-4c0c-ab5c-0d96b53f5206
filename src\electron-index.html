<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YSViewer - YouTube & Spotify Player</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .spotify-login-section {
            display: flex;
            gap: 10px;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px 12px;
            backdrop-filter: blur(10px);
        }

        .spotify-login-section .status-text.checking {
            font-style: italic;
            color: rgba(255, 255, 255, 0.7);
        }

        .spinner {
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin { to { transform: rotate(360deg); } }

        .spotify-login-section .status-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-right: 5px;
        }

        .spotify-input {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            padding: 6px 8px;
            color: #ffffff;
            font-size: 12px;
            width: 100px;
        }

        .spotify-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .spotify-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.3);
        }

        .spotify-btn {
            background: rgba(30, 215, 96, 0.8);
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            color: #ffffff;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .spotify-btn:hover {
            background: rgba(30, 215, 96, 1);
            transform: translateY(-1px);
        }

        .spotify-btn:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
        }

        .spotify-btn.logout {
            background: rgba(255, 87, 87, 0.8);
        }

        .spotify-btn.logout:hover {
            background: rgba(255, 87, 87, 1);
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            color: #ffffff;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .control-btn.active {
            background: rgba(255, 255, 255, 0.4);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .browser-views {
            height: calc(100vh - 100px);
            position: relative;
            background: #000;
        }

        .view-labels {
            position: absolute;
            top: 10px;
            left: 0;
            right: 0;
            display: flex;
            z-index: 100;
            pointer-events: none;
        }

        .view-label {
            flex: 1;
            text-align: center;
            background: rgba(0, 0, 0, 0.7);
            color: #ffffff;
            padding: 8px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(5px);
        }

        .view-label.youtube {
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .address-bars {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            display: flex;
            z-index: 100;
            pointer-events: auto;
        }

        .address-bar {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 8px 12px;
            font-size: 11px;
            font-family: 'Courier New', monospace;
            border: none;
            outline: none;
            backdrop-filter: blur(5px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .address-bar.youtube {
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .address-bar:focus {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }

        .notification {
            position: fixed;
            top: 120px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid #4CAF50;
        }

        .notification.error {
            border-left: 4px solid #f44336;
        }

        .notification.info {
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <div class="logo-icon">🎵</div>
            <h1>YSViewer</h1>
        </div>

        <div class="status-indicator">
            <div class="status-dot"></div>
            <span id="status-text">Ready</span>
        </div>
        
        <div class="controls">

            <div class="spotify-login-section" id="spotify-status-view">
                <div class="spinner"></div>
                <span class="status-text checking">Checking Spotify status...</span>
            </div>
            <div class="spotify-login-section" id="spotify-login-view" style="display: none;">
                <input type="text" id="spotify-email" class="spotify-input" placeholder="Spotify Email">
                <input type="password" id="spotify-password" class="spotify-input" placeholder="Password">
                <button id="spotify-login-btn" class="spotify-btn">Login</button>
            </div>
            <div class="spotify-login-section" id="spotify-logout-view" style="display: none;">
                <span id="spotify-status-text" class="status-text"></span>
                <button id="spotify-logout-btn" class="spotify-btn logout">Logout</button>
            </div>

            <button class="control-btn" id="audio-btn" title="Toggle System Audio">
                <span id="audio-icon">🔊</span>
                <span id="audio-text">Audio</span>
            </button>

            <button class="control-btn" id="minimize-btn" title="Minimize to Tray">
                <span>📥</span>
                <span>To Tray</span>
            </button>

            <button class="control-btn" id="close-btn" title="Close Application">
                <span>✕</span>
                <span>Close</span>
            </button>
        </div>
    </div>

    <div class="browser-views">
        <div class="view-labels">
            <div class="view-label youtube">YouTube</div>
            <div class="view-label spotify">Spotify</div>
        </div>
        <div class="address-bars">
            <input type="text" class="address-bar youtube" id="youtube-address" readonly placeholder="Loading YouTube..." />
            <input type="text" class="address-bar spotify" id="spotify-address" readonly placeholder="Loading Spotify..." />
        </div>
    </div>

    <div class="notification" id="notification">
        <span id="notification-text"></span>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
