const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

// Create red circular icon with exact color #EE4B2B
function createRedIcon() {
  // Create a 32x32 canvas for high quality
  const size = 32;
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Clear canvas with transparent background
  ctx.clearRect(0, 0, size, size);

  // Set up circle parameters
  const centerX = size / 2;
  const centerY = size / 2;
  const radius = size / 2 - 2; // Leave 2px border

  // Create circular path
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);

  // Fill with exact red color #EE4B2B
  ctx.fillStyle = '#EE4B2B'; // RGB(238, 75, 43)
  ctx.fill();

  // Add subtle border for better visibility
  ctx.strokeStyle = '#CC3A1A'; // Slightly darker red for border
  ctx.lineWidth = 1;
  ctx.stroke();

  // Save as PNG
  const buffer = canvas.toBuffer('image/png');
  const outputPath = path.join(__dirname, 'icon-red.png');
  fs.writeFileSync(outputPath, buffer);
  
  console.log(`Red icon created: ${outputPath}`);
  console.log(`Color used: #EE4B2B (RGB: 238, 75, 43)`);
  console.log(`Size: ${size}x${size} pixels`);
}

// Create green circular icon for normal state
function createGreenIcon() {
  // Create a 32x32 canvas for high quality
  const size = 32;
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // Clear canvas with transparent background
  ctx.clearRect(0, 0, size, size);

  // Set up circle parameters
  const centerX = size / 2;
  const centerY = size / 2;
  const radius = size / 2 - 2; // Leave 2px border

  // Create circular path
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);

  // Fill with green color
  ctx.fillStyle = '#00C800'; // Bright green
  ctx.fill();

  // Add subtle border for better visibility
  ctx.strokeStyle = '#00A000'; // Slightly darker green for border
  ctx.lineWidth = 1;
  ctx.stroke();

  // Save as PNG
  const buffer = canvas.toBuffer('image/png');
  const outputPath = path.join(__dirname, 'icon-green.png');
  fs.writeFileSync(outputPath, buffer);
  
  console.log(`Green icon created: ${outputPath}`);
  console.log(`Color used: #00C800 (RGB: 0, 200, 0)`);
  console.log(`Size: ${size}x${size} pixels`);
}

// Check if canvas is available
try {
  createRedIcon();
  createGreenIcon();
  console.log('Icons created successfully!');
} catch (error) {
  console.error('Error creating icons:', error);
  console.log('Make sure to install canvas: npm install canvas');
}
